{"name": "react-demo-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "json-server --watch db.json --port 3001", "dev:full": "concurrently \"npm run server\" \"npm run dev\""}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tailwindcss/postcss": "^4.1.7", "autoprefixer": "^10.4.21", "json-server": "^1.0.0-beta.3", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}