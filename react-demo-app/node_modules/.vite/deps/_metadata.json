{"hash": "4ba49f1a", "configHash": "3af78719", "lockfileHash": "bbe9dcec", "browserHash": "4f235786", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "d4b1ec10", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "5e2671da", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "611b3cec", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "1de94e25", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "86018470", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "f8cd6de0", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "73e00f75", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "16ff81ea", "needsInterop": false}}, "chunks": {"chunk-N4JV66NV": {"file": "chunk-N4JV66NV.js"}, "chunk-CBG3MKAY": {"file": "chunk-CBG3MKAY.js"}, "chunk-EQCVQC35": {"file": "chunk-EQCVQC35.js"}}}